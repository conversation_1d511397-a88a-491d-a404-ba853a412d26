'use client';

import React, { useEffect, useState, useRef, useCallback } from 'react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { Play, Star, Trophy, Clock, Users, ChevronLeft, Share2, Maximize, Gamepad, Info, Sparkles, MousePointer, Keyboard } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { gamesData, GameCardProps, getAllGameIds } from '@/data/games';
import { getGameDetailById, GameDetailProps } from '@/data/game-details';

// 定义本地化文本类型 - 用于辅助函数
type LocalizedText = {
  en: string;
  zh: string;
  [key: string]: string;
};

// 定义本地化数组类型 - 用于辅助函数
type LocalizedArray = {
  en: string[];
  zh: string[];
  [key: string]: string[];
};

// 扩展 GameCardProps 类型
interface GameCardWithScore extends GameCardProps {
  similarityScore?: number;
}

// 获取本地化文本的辅助函数
function getLocalizedText(text: Partial<LocalizedText> | undefined, locale: string): string {
  if (!text) return '';
  return text[locale] || text['en'] || '';
}

// 获取本地化数组的辅助函数
function getLocalizedArray(array: Partial<LocalizedArray> | undefined, locale: string): string[] {
  if (!array) return [];
  return array[locale] || array['en'] || [];
}


// 计算游戏相似度分数
function calculateSimilarityScore(game1: GameCardProps, game2: GameCardProps): number {
  let score = 0;
  
  // 类别匹配度 (40%)
  if (game1.category.en === game2.category.en) {
    score += 40;
  }
  
  // 评分接近度 (30%)
  const ratingDiff = Math.abs(game1.rating - game2.rating);
  if (ratingDiff <= 0.5) {
    score += 30;
  } else if (ratingDiff <= 1) {
    score += 20;
  } else if (ratingDiff <= 1.5) {
    score += 10;
  }
  
  // 标题关键词匹配度 (30%)
  const title1 = game1.title.en.toLowerCase();
  const title2 = game2.title.en.toLowerCase();
  const words1 = title1.split(' ');
  const words2 = title2.split(' ');
  
  const commonWords = words1.filter(word => words2.includes(word));
  const matchPercentage = commonWords.length / Math.max(words1.length, words2.length);
  score += Math.round(matchPercentage * 30);
  
  return score;
}

// 获取相似游戏
function getSimilarGames(currentGame: GameCardProps, allGames: Record<string, GameCardProps[]>): GameCardWithScore[] {
  const similarGames: GameCardWithScore[] = [];
  
  // 遍历所有游戏类别
  Object.values(allGames).forEach(games => {
    games.forEach(game => {
      // 排除当前游戏
      if (game.id !== currentGame.id) {
        const score = calculateSimilarityScore(currentGame, game);
        similarGames.push({ ...game, similarityScore: score });
      }
    });
  });
  
  // 按相似度分数排序并返回前3个
  return similarGames
    .sort((a, b) => (b.similarityScore || 0) - (a.similarityScore || 0))
    .slice(0, 8);
}

export default function GameDetailPage() {
  const params = useParams();
  const router = useRouter();
  const gameId = params.id as string;
  const [selectedImage, setSelectedImage] = useState<string>('');
  const [activeTab, setActiveTab] = useState<'intro' | 'features' | 'controls'>('intro');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [audioContextInitialized, setAudioContextInitialized] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  const locale = params.locale as string;
  const game = getGameDetailById(gameId);
  const t = useTranslations('GameDetail');

  // 客户端水合标记
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 初始化音频上下文
  const initAudioContext = useCallback(() => {
    if (!audioContextInitialized && typeof window !== 'undefined') {
      const AudioContext = window.AudioContext || (window as any).webkitAudioContext;
      if (AudioContext) {
        const audioCtx = new AudioContext();
        if (audioCtx.state === 'suspended') {
          audioCtx.resume();
        }
        setAudioContextInitialized(true);
      }
    }
  }, [audioContextInitialized]);

  useEffect(() => {
    const handleUserInteraction = () => {
      initAudioContext();
      // 移除事件监听器
      document.removeEventListener('click', handleUserInteraction);
      document.removeEventListener('keydown', handleUserInteraction);
      document.removeEventListener('touchstart', handleUserInteraction);
    };

    if (typeof window !== 'undefined') {
      document.addEventListener('click', handleUserInteraction);
      document.addEventListener('keydown', handleUserInteraction);
      document.addEventListener('touchstart', handleUserInteraction);

      return () => {
        document.removeEventListener('click', handleUserInteraction);
        document.removeEventListener('keydown', handleUserInteraction);
        document.removeEventListener('touchstart', handleUserInteraction);
      };
    }
  }, [initAudioContext]);

  useEffect(() => {
    if (game) {
      setSelectedImage(game.image);
    }
  }, [game]);

  const handleFullscreen = useCallback(() => {
    if (iframeRef.current && typeof document !== 'undefined') {
      if (iframeRef.current.requestFullscreen) {
        iframeRef.current.requestFullscreen();
        setIsFullscreen(true);
      }
    }
  }, []);

  // 预加载游戏URL
  useEffect(() => {
    if (game?.gameUrl) {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'document';
      link.href = game.gameUrl;
      document.head.appendChild(link);
    }
  }, [game?.gameUrl]);

  // iframe加载处理
  const handleIframeLoad = useCallback(() => {
    setIsLoading(false);
  }, []);

  // 验证游戏ID是否有效
  const isValidGameId = getAllGameIds().includes(gameId);

  if (!isValidGameId || !game) {
    return (
      <div className="container mx-auto px-4 py-12">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">{t('gameNotFound')}</h1>
          <Link href={`/${locale}`} className="text-primary hover:underline">
            {t('returnHome')}
          </Link>
        </div>
      </div>
    );
  }

  const similarGames = game ? getSimilarGames(game, gamesData) : [];

  return (
    <AnimatePresence mode="wait">
      <motion.div 
        key={gameId}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="container mx-auto px-4 py-8"
      >
        <Link 
          href={`/${locale}`}
          className="inline-flex items-center text-sm text-muted-foreground hover:text-foreground mb-4 transition-colors"
        >
          <ChevronLeft className="h-4 w-4 mr-1" />
          {t('returnHome')}
        </Link>
        
        {/* 游戏标题和评分 */}
        <div className="flex items-start justify-between mb-4">
          <div>
            <h1 className="text-3xl md:text-4xl font-bold font-display mb-2">
              {getLocalizedText(game.title, locale)}
            </h1>
            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
              <span>{getLocalizedText(game.category, locale)}</span>
              <span className="flex items-center">
                <Star className="h-4 w-4 fill-yellow-400 stroke-yellow-400 mr-1" />
                {game.rating.toFixed(1)}
              </span>
              <span>{getLocalizedText(game.players, locale)}</span>
            </div>
          </div>
          <button className="p-2 rounded-full hover:bg-secondary transition-colors" title={t('share')}>
            <Share2 className="h-5 w-5" />
          </button>
        </div>
        
        {/* 游戏iframe - 优化加载 */}
        {isClient && (
          <div className="relative mb-8 bg-card rounded-lg overflow-hidden shadow-lg">
            <div className="aspect-video w-full relative">
              {isLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-secondary/50">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
                </div>
              )}
              <iframe
                ref={iframeRef}
                id="game-iframe"
                src={game.gameUrl}
                className="absolute w-full h-full border-none"
                allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture; fullscreen"
                allowFullScreen
                title={getLocalizedText(game.title, locale)}
                onLoad={handleIframeLoad}
                sandbox="allow-scripts allow-same-origin allow-popups"
              />
              
              <div className="absolute bottom-4 right-4 flex space-x-2">
                <button 
                  onClick={handleFullscreen}
                  className="p-2 bg-black/70 text-white rounded-full hover:bg-black/90 transition-all"
                  title={t('fullscreen')}
                >
                  <Maximize className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>
        )}
        
        {/* 标签导航 */}
        <div className="flex border-b border-border mb-8">
          <button
            className={`px-6 py-3 font-medium text-sm -mb-px transition-all duration-200 ${
              activeTab === 'intro'
                ? 'border-b-2 border-primary text-primary bg-primary/5'
                : 'text-muted-foreground hover:text-foreground hover:bg-secondary/50'
            }`}
            onClick={() => setActiveTab('intro')}
          >
            <Info className="h-4 w-4 inline mr-2" />
            {t('gameIntro')}
          </button>
          <button
            className={`px-6 py-3 font-medium text-sm -mb-px transition-all duration-200 ${
              activeTab === 'features'
                ? 'border-b-2 border-primary text-primary bg-primary/5'
                : 'text-muted-foreground hover:text-foreground hover:bg-secondary/50'
            }`}
            onClick={() => setActiveTab('features')}
          >
            <Sparkles className="h-4 w-4 inline mr-2" />
            {t('gameFeatures')}
          </button>
          <button
            className={`px-6 py-3 font-medium text-sm -mb-px transition-all duration-200 ${
              activeTab === 'controls'
                ? 'border-b-2 border-primary text-primary bg-primary/5'
                : 'text-muted-foreground hover:text-foreground hover:bg-secondary/50'
            }`}
            onClick={() => setActiveTab('controls')}
          >
            <Gamepad className="h-4 w-4 inline mr-2" />
            {t('controls')}
          </button>
        </div>
        
        {/* 内容区域 */}
        <div className="mb-12">
          {activeTab === 'intro' && (
            <div>
              {/* 游戏描述 - 使用 Markdown 渲染 */}
              <div className="prose prose-lg max-w-none mb-8 dark:prose-invert">
                <div className="bg-card/50 rounded-xl p-6 border border-border/50 shadow-sm">
                  <ReactMarkdown
                    remarkPlugins={[remarkGfm]}
                    components={{
                      p: ({ children }) => (
                        <p className="mb-4 text-base leading-7 text-muted-foreground last:mb-0">
                          {children}
                        </p>
                      ),
                      h1: ({ children }) => (
                        <h1 className="text-2xl font-bold mb-4 text-foreground">
                          {children}
                        </h1>
                      ),
                      h2: ({ children }) => (
                        <h2 className="text-xl font-semibold mb-3 text-foreground">
                          {children}
                        </h2>
                      ),
                      h3: ({ children }) => (
                        <h3 className="text-lg font-medium mb-2 text-foreground">
                          {children}
                        </h3>
                      ),
                      ul: ({ children }) => (
                        <ul className="list-disc list-inside mb-4 space-y-2 text-muted-foreground">
                          {children}
                        </ul>
                      ),
                      ol: ({ children }) => (
                        <ol className="list-decimal list-inside mb-4 space-y-2 text-muted-foreground">
                          {children}
                        </ol>
                      ),
                      li: ({ children }) => (
                        <li className="text-base leading-6">
                          {children}
                        </li>
                      ),
                      strong: ({ children }) => (
                        <strong className="font-semibold text-foreground">
                          {children}
                        </strong>
                      ),
                      em: ({ children }) => (
                        <em className="italic text-foreground">
                          {children}
                        </em>
                      ),
                      blockquote: ({ children }) => (
                        <blockquote className="border-l-4 border-primary/30 pl-4 italic text-muted-foreground mb-4">
                          {children}
                        </blockquote>
                      ),
                      code: ({ children }) => (
                        <code className="bg-secondary/80 px-1.5 py-0.5 rounded text-sm font-mono text-foreground">
                          {children}
                        </code>
                      ),
                    }}
                  >
                    {getLocalizedText(game.longDescription, locale)}
                  </ReactMarkdown>
                </div>
              </div>

              {/* 游戏信息卡片 */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
                <div className="p-5 rounded-xl bg-gradient-to-br from-secondary/60 to-secondary/40 border border-border/50 shadow-sm hover:shadow-md transition-shadow">
                  <div className="flex items-center space-x-3 text-sm font-medium mb-2">
                    <div className="p-2 rounded-lg bg-primary/10">
                      <Clock className="h-4 w-4 text-primary" />
                    </div>
                    <span className="text-foreground">{t('playtime')}</span>
                  </div>
                  <p className="text-sm text-muted-foreground font-medium">{game.playTime[locale] || game.playTime['en']}</p>
                </div>
                <div className="p-5 rounded-xl bg-gradient-to-br from-secondary/60 to-secondary/40 border border-border/50 shadow-sm hover:shadow-md transition-shadow">
                  <div className="flex items-center space-x-3 text-sm font-medium mb-2">
                    <div className="p-2 rounded-lg bg-primary/10">
                      <Users className="h-4 w-4 text-primary" />
                    </div>
                    <span className="text-foreground">{t('players')}</span>
                  </div>
                  <p className="text-sm text-muted-foreground font-medium">{game.players[locale] || game.players['en']}</p>
                </div>
                <div className="p-5 rounded-xl bg-gradient-to-br from-secondary/60 to-secondary/40 border border-border/50 shadow-sm hover:shadow-md transition-shadow">
                  <div className="flex items-center space-x-3 text-sm font-medium mb-2">
                    <div className="p-2 rounded-lg bg-primary/10">
                      <Trophy className="h-4 w-4 text-primary" />
                    </div>
                    <span className="text-foreground">{t('difficulty')}</span>
                  </div>
                  <p className="text-sm text-muted-foreground font-medium">{getLocalizedText(game.difficulty, locale)}</p>
                </div>
              </div>

              {/* 游戏截图 */}
              <div className="mt-8">
                <h3 className="text-2xl font-bold mb-6 text-foreground">{t('screenshots')}</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div className="aspect-video rounded-xl overflow-hidden bg-secondary shadow-lg hover:shadow-xl transition-shadow duration-300 group">
                    <Image
                      src={game?.image || ''}
                      alt={getLocalizedText(game?.title, locale)}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                      width={400}
                      height={225}
                    />
                  </div>
                  {game.screenshots.map((screenshot, index) => (
                    <div key={index} className="aspect-video rounded-xl overflow-hidden bg-secondary shadow-lg hover:shadow-xl transition-shadow duration-300 group">
                      <Image
                        src={screenshot}
                        alt={`${getLocalizedText(game?.title, locale)} screenshot ${index + 1}`}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                        width={400}
                        height={225}
                      />
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
          
          {activeTab === 'features' && (
            <div>
              <h3 className="text-2xl font-bold mb-6 text-foreground">{t('gameFeatures')}</h3>
              <div className="bg-card/50 rounded-xl p-6 border border-border/50 shadow-sm">
                <ul className="space-y-4">
                  {game.features && getLocalizedArray(game.features, locale).map((feature, index) => (
                    <li key={index} className="flex items-start group">
                      <span className="inline-flex items-center justify-center h-8 w-8 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 text-primary mr-4 mt-0.5 group-hover:from-primary/30 group-hover:to-primary/20 transition-all duration-200">
                        <Sparkles className="h-4 w-4" />
                      </span>
                      <span className="text-base leading-7 text-muted-foreground group-hover:text-foreground transition-colors duration-200">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          )}
          
          {activeTab === 'controls' && (
            <div>
              <h3 className="text-2xl font-bold mb-6 text-foreground">{t('controls')}</h3>

              {game.controls?.keyboard && (
                <div className="mb-8">
                  <h4 className="text-lg font-semibold flex items-center mb-4 text-foreground">
                    <div className="p-2 rounded-lg bg-primary/10 mr-3">
                      <Keyboard className="h-5 w-5 text-primary" />
                    </div>
                    {t('keyboardControls')}
                  </h4>
                  <div className="bg-card/50 rounded-xl p-6 border border-border/50 shadow-sm">
                    <ul className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {getLocalizedArray(game.controls.keyboard, locale).map((control, index) => (
                        <li key={index} className="flex items-center py-3 px-4 bg-gradient-to-r from-secondary/60 to-secondary/40 rounded-lg border border-border/30 hover:from-secondary/80 hover:to-secondary/60 transition-all duration-200">
                          <span className="text-sm font-medium text-foreground">{control}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}

              {game.controls?.mouse && (
                <div>
                  <h4 className="text-lg font-semibold flex items-center mb-4 text-foreground">
                    <div className="p-2 rounded-lg bg-primary/10 mr-3">
                      <MousePointer className="h-5 w-5 text-primary" />
                    </div>
                    {t('mouseControls')}
                  </h4>
                  <div className="bg-card/50 rounded-xl p-6 border border-border/50 shadow-sm">
                    <ul className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {getLocalizedArray(game.controls.mouse, locale).map((control, index) => (
                        <li key={index} className="flex items-center py-3 px-4 bg-gradient-to-r from-secondary/60 to-secondary/40 rounded-lg border border-border/30 hover:from-secondary/80 hover:to-secondary/60 transition-all duration-200">
                          <span className="text-sm font-medium text-foreground">{control}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
        
        {/* 相似游戏 */}
        <div className="mt-12">
          <h2 className="text-2xl font-bold mb-6">{t('similarGames')}</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {similarGames.map((similarGame) => (
              <Link 
                key={similarGame.id} 
                href={`/${locale}/game/${similarGame.id}`}
                target="_blank"
                rel="noopener noreferrer"
              >
                <div className="group cursor-pointer card-hover">
                  <div className="aspect-video rounded-lg overflow-hidden bg-secondary">
                    <Image 
                      src={similarGame?.image || ''} 
                      alt={getLocalizedText(similarGame?.title, locale)} 
                      className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                      width={320}
                      height={180}
                    />
                  </div>
                  <div className="mt-2">
                    <h3 className="font-medium">{getLocalizedText(similarGame.title, locale)}</h3>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <span>{getLocalizedText(similarGame.category, locale)}</span>
                      <span className="mx-2">•</span>
                      <span className="flex items-center">
                        <Star className="h-3 w-3 fill-yellow-400 stroke-yellow-400 mr-1" />
                        {similarGame.rating}
                      </span>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
}