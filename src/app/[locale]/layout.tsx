import {NextIntlClientProvider} from 'next-intl';
import {getMessages} from '@/i18n/routing';
import {notFound} from 'next/navigation';
import {routing} from '@/i18n/routing';
import '../globals.css';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { getLandingPage } from '@/app/actions';
import { Providers } from '@/app/providers';
import Script from 'next/script';
import { Metadata, Viewport } from 'next';
// import { SplashCursor } from "@/components/ui/splash-cursor"


// 从 routing 中获取具体的 locale 类型
type Locale = (typeof routing.locales)[number];

// 修改类型定义，使用 generateStaticParams 来处理参数
export async function generateStaticParams() {
  return routing.locales.map((locale) => ({locale}));
}

// 添加视口配置
export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  themeColor: '#f4511e', // 主题色，与品牌颜色一致
}

// 添加元数据配置
export const metadata: Metadata = {
  metadataBase: new URL('https://freehubgames.com'),
  title: {
    template: '%s | FreeHubGames - Play Free Online Games',
    default: 'FreeHubGames - Play Free Online Games | Best Free Games Collection',
  },
  description: 'Play free online games at FreeHubGames.com. Discover 1000+ fun browser games for all ages - action, puzzle, strategy, racing & more. No download required, play instantly for free!',
  keywords: [
    'play free games', 'free online games', 'browser games', 'free games online', 
    'freehubgames', 'no download games', 'html5 games', 'mobile games', 
    'action games', 'puzzle games', 'strategy games', 'racing games', 
    'adventure games', 'arcade games', 'kids games', 'multiplayer games',
    'best free games', 'free games collection', 'play games without download'
  ],
  creator: 'FreeHubGames Team',
  publisher: 'FreeHubGames.com',
  category: 'Gaming',
  openGraph: {
    title: 'FreeHubGames - Play Free Online Games | Best Free Games Collection',
    description: 'Play 1000+ free online games at FreeHubGames.com. Instant play, no download, mobile-friendly browser games for everyone. New games added daily!',
    url: 'https://freehubgames.com',
    siteName: 'FreeHubGames',
    locale: 'en_US',
    type: 'website',
    images: [
      {
        url: '/images/og-image.png',
        width: 1200,
        height: 630,
        alt: 'FreeHubGames - Play Free Online Games',
      }
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'FreeHubGames - Play Free Online Games',
    description: 'Instant access to 1000+ free browser games for all ages and interests. Play now with no signup required!',
    images: ['/images/twitter-image.png'],
    creator: '@freehubgames',
    site: '@freehubgames',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  alternates: {
    canonical: 'https://freehubgames.com',
    languages: {
      'en': 'https://freehubgames.com/en',
      'zh': 'https://freehubgames.com/zh',
    },
  },
  verification: {
    google: 'your-google-verification-code', // 替换为您的Google验证码
  },
  applicationName: 'FreeHubGames',
  referrer: 'origin-when-cross-origin',
  authors: [{ name: 'FreeHubGames Team', url: 'https://freehubgames.com/about' }],
  generator: 'Next.js',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
}

// 更新 Layout 组件以支持异步 params
export default async function Layout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  // 使用 await 获取 locale
  const { locale } = await params;
  
  // 验证 locale
  if (!routing.locales.includes(locale as any)) {
    notFound();
  }
 
  // 并行获取消息和页面数据
  const [messages, page] = await Promise.all([
    getMessages(locale),
    getLandingPage(locale)
  ]);

  return (
    <html lang={locale} className="scroll-smooth" itemScope itemType="https://schema.org/WebPage">
      {/* 鼠标烟雾特效 */}
      {/* <SplashCursor /> */}

      <head>
        {/* Google Analytics */}
        <Script
          src="https://www.googletagmanager.com/gtag/js?id=G-26XTZ23KYG"
          strategy="afterInteractive"
        />
        <Script id="google-analytics" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-26XTZ23KYG');
          `}
        </Script>
        {/* Additional SEO Structured Data */}
        <Script id="structured-data" type="application/ld+json">
          {`
            [
              {
                "@context": "https://schema.org",
                "@type": "WebSite",
                "name": "FreeHubGames",
                "alternateName": "Free Games Hub",
                "url": "https://freehubgames.com",
                "potentialAction": {
                  "@type": "SearchAction",
                  "target": "https://freehubgames.com/search?q={search_term_string}",
                  "query-input": "required name=search_term_string"
                },
                "description": "Play free online games at FreeHubGames.com. Discover 1000+ fun browser games for all ages - no download required, play instantly!",
                "inLanguage": "${locale}",
                "sameAs": [
                  "https://twitter.com/freehubgames",
                  "https://facebook.com/freehubgames"
                ]
              },
              {
                "@context": "https://schema.org",
                "@type": "Organization",
                "name": "FreeHubGames",
                "url": "https://freehubgames.com",
                "logo": "https://freehubgames.com/images/logo.png",
                "contactPoint": {
                  "@type": "ContactPoint",
                  "contactType": "customer support",
                  "email": "<EMAIL>"
                }
              },
              {
                "@context": "https://schema.org",
                "@type": "BreadcrumbList",
                "itemListElement": [
                  {
                    "@type": "ListItem",
                    "position": 1,
                    "name": "Home",
                    "item": "https://freehubgames.com/${locale}"
                  }
                ]
              }
            ]
          `}
        </Script>
        
        {/* Preload critical resources */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        
        {/* Responsive meta tag - important for mobile SEO */}
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
        
        {/* Favicons */}
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
      </head>

      <body>
          <Script
            src="https://umami.wenhaofree.com/script.js"
            data-website-id="5ec8fd57-cb91-4407-84d0-c9fa873cf41b"
            defer
          />
          <NextIntlClientProvider messages={messages} locale={locale}>
          <Providers>
            <div className="fixed inset-x-0 top-0 z-50 h-16 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                <div className="h-full">
                  {page.header && <Header header={page.header} />}
                </div>
            </div>
            <main className="flex-1 pt-16">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                  {children}
                </div>
              </main>
            <div className="border-t">
              {page.footer && <Footer footer={page.footer} />}
            </div>
            </Providers>
          </NextIntlClientProvider>
        
      </body>
    </html>
  );
}