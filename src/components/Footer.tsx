"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { locales, localeNames } from "@/i18n/routing";
import { motion } from "framer-motion";
import { Facebook, Twitter, Instagram, Youtube, Mail, Phone, MapPin } from "lucide-react";
import { useTranslations } from "next-intl";

interface FooterProps {
  footer: {
    copyright: string;
    about: {
      title: string;
      about: string;
      blog: string;
    };
    support: {
      title: string;
      helpCenter: string;
      contactUs: string;
    };
    legal: {
      title: string;
      privacy: string;
      terms: string;
    };
    language: {
      title: string;
      english: string;
      chinese: string;
      japanese: string;
      korean: string;
      french: string;
    };
  };
}

export default function Footer({ footer }: FooterProps) {
  const pathname = usePathname();
  const currentLocale = pathname.split('/')[1];
  const t = useTranslations('Footer');
  const tNav = useTranslations('Navigation');

  const switchLocale = (locale: string) => {
    const newPathname = pathname.replace(`/${currentLocale}`, `/${locale}`);
    window.location.href = newPathname;
  };

  // 动画变量
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  };

  // 游戏分类数据 - 与Header保持一致
  const gameCategories = [
    { id: 'action', name: tNav('categoryList.action'), icon: '🎮' },
    { id: 'adventure', name: tNav('categoryList.adventure'), icon: '🗺️' },
    { id: 'racing', name: tNav('categoryList.racing'), icon: '🏎️' },
    { id: 'shooting', name: tNav('categoryList.shooting'), icon: '🔫' },
    { id: 'horror', name: tNav('categoryList.horror'), icon: '👻' },
    { id: 'strategy', name: tNav('categoryList.strategy'), icon: '⚔️' },
    { id: 'sports', name: tNav('categoryList.sports'), icon: '⚽' },
    { id: 'simulation', name: tNav('categoryList.simulation'), icon: '🎯' },
    { id: 'puzzle', name: tNav('categoryList.puzzle'), icon: '🧩' },
    { id: 'sandbox', name: tNav('categoryList.sandbox'), icon: '🏗️' }
  ];

  return (
    <footer className="bg-secondary/30">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {/* 主要链接和信息 */}
        <motion.div 
          variants={container}
          initial="hidden"
          whileInView="show"
          viewport={{ once: true, amount: 0.3 }}
          className="grid gap-8 py-12 md:grid-cols-2 lg:grid-cols-4"
        >
          {/* 公司信息 */}
          <motion.div variants={item} className="space-y-4">
            <div className="flex items-center mb-4">
              <span className="text-xl font-bold">Free</span>
              <span className="text-xl font-bold text-gradient">Hub</span>
              <span className="text-xl font-bold">Games</span>
            </div>
            <p className="text-sm text-muted-foreground mb-4">
              {t('about')}
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-foreground hover:text-foreground/80 transition-colors" aria-label={t('followFacebook')}>
                <Facebook className="h-5 w-5" aria-hidden="true" />
              </a>
              <a href="#" className="text-foreground hover:text-foreground/80 transition-colors" aria-label={t('followTwitter')}>
                <Twitter className="h-5 w-5" aria-hidden="true" />
              </a>
              <a href="#" className="text-foreground hover:text-foreground/80 transition-colors" aria-label={t('followInstagram')}>
                <Instagram className="h-5 w-5" aria-hidden="true" />
              </a>
              <a href="#" className="text-foreground hover:text-foreground/80 transition-colors" aria-label={t('followYoutube')}>
                <Youtube className="h-5 w-5" aria-hidden="true" />
              </a>
            </div>
          </motion.div>

          {/* 游戏分类 */}
          <motion.div variants={item} className="space-y-4">
            <h3 className="text-lg font-medium text-foreground">{t('categories')}</h3>
            <ul className="space-y-3">
              {gameCategories.map(category => (
                <motion.li key={category.id} variants={item}>
                  <Link
                    href={`/${currentLocale}/games/${category.id}`}
                    className="flex items-center gap-2 text-sm text-foreground hover:text-foreground/80 transition-colors"
                    aria-label={`${t('view')} ${category.name} ${t('games')}`}
                  >
                    <span className="text-base">{category.icon}</span>
                    <span>{category.name}</span>
                  </Link>
                </motion.li>
              ))}
              <motion.li variants={item}>
                <Link
                  href={`/${currentLocale}/popular`}
                  className="flex items-center gap-2 text-sm text-foreground hover:text-foreground/80 transition-colors"
                  aria-label={t('popular')}
                >
                  <span className="text-base">🔥</span>
                  <span>{tNav('popular')}</span>
                </Link>
              </motion.li>
              <motion.li variants={item}>
                <Link
                  href={`/${currentLocale}/new`}
                  className="flex items-center gap-2 text-sm text-foreground hover:text-foreground/80 transition-colors"
                  aria-label={t('new')}
                >
                  <span className="text-base">✨</span>
                  <span>{tNav('new')}</span>
                </Link>
              </motion.li>
            </ul>
          </motion.div>

          {/* 关于我们和支持 */}
          {/* <motion.div variants={item} className="space-y-4"> */}
            {/* <h3 className="text-lg font-medium text-foreground">{footer.about.title}</h3> */}
            {/* <ul className="space-y-3"> */}
              {/* <motion.li variants={item}>
                <Link href={`/${currentLocale}/about`} className="text-sm text-foreground hover:text-foreground/80 transition-colors">
                  {footer.about.about}
                </Link>
              </motion.li>
              <motion.li variants={item}>
                <Link href={`/${currentLocale}/blog`} className="text-sm text-foreground hover:text-foreground/80 transition-colors">
                  {footer.about.blog}
                </Link>
              </motion.li>
              <motion.li variants={item}>
                <Link href={`/${currentLocale}/help`} className="text-sm text-foreground hover:text-foreground/80 transition-colors">
                  {footer.support.helpCenter}
                </Link>
              </motion.li>
              <motion.li variants={item}>
                <Link href={`/${currentLocale}/contact`} className="text-sm text-foreground hover:text-foreground/80 transition-colors">
                  {footer.support.contactUs}
                </Link>
              </motion.li> */}
            {/* </ul> */}
          {/* </motion.div> */}

          {/* 联系我们和语言 */}
          <motion.div variants={item} className="space-y-4">
            {/* <h3 className="text-lg font-medium text-foreground">{t('contact')}</h3>
            <ul className="space-y-3">
              <motion.li variants={item} className="flex items-center">
                <Mail className="h-4 w-4 mr-2 text-foreground" aria-hidden="true" />
                <a href="mailto:<EMAIL>" className="text-sm text-foreground hover:text-foreground/80 transition-colors" aria-label={t('emailUs')}>
                  {t('contactEmail')}
                </a>
              </motion.li>
              <motion.li variants={item} className="flex items-center">
                <Phone className="h-4 w-4 mr-2 text-foreground" aria-hidden="true" />
                <a href="tel:+1234567890" className="text-sm text-foreground hover:text-foreground/80 transition-colors" aria-label={t('callUs')}>
                  {t('contactPhone')}
                </a>
              </motion.li>
              <motion.li variants={item} className="flex items-center">
                <MapPin className="h-4 w-4 mr-2 text-foreground" aria-hidden="true" />
                <span className="text-sm text-foreground">
                  {t('contactAddress')}
                </span>
              </motion.li>
            </ul> */}
            
            <h3 className="text-lg font-medium text-foreground mt-6">{footer.language.title}</h3>
            <ul className="space-y-2">
              {locales.map((locale) => (
                <motion.li key={locale} variants={item}>
                  <button
                    onClick={() => switchLocale(locale)}
                    className={`text-sm text-foreground hover:text-foreground/80 transition-colors w-full text-left`}
                    aria-label={`${t('switchLanguage')} ${localeNames[locale]}`}
                  >
                    {localeNames[locale]}
                  </button>
                </motion.li>
              ))}
            </ul>
          </motion.div>
        </motion.div>

        {/* 底部版权和链接 */}
        <motion.div 
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
          viewport={{ once: true }}
          className="border-t border-border py-6"
        >
          <div className="flex flex-col items-center justify-between gap-4 md:flex-row">
            <p className="text-sm text-muted-foreground">
              &copy; {new Date().getFullYear()} GameGrove - {footer.copyright}
            </p>
            <div className="flex space-x-6">
              <Link href={`/${currentLocale}/privacy`} className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                {footer.legal.privacy}
              </Link>
              <Link href={`/${currentLocale}/terms`} className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                {footer.legal.terms}
              </Link>
              <Link href={`/${currentLocale}/sitemap`} className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                {t('sitemap')}
              </Link>
            </div>
          </div>
        </motion.div>
      </div>
    </footer>
  );
}